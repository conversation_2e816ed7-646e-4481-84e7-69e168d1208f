"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Ece4180Stack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cognito = __importStar(require("aws-cdk-lib/aws-cognito"));
const apigateway = __importStar(require("aws-cdk-lib/aws-apigateway"));
const lambda = __importStar(require("aws-cdk-lib/aws-lambda"));
const dynamodb = __importStar(require("aws-cdk-lib/aws-dynamodb"));
const s3 = __importStar(require("aws-cdk-lib/aws-s3"));
const iam = __importStar(require("aws-cdk-lib/aws-iam"));
const lab_content_importer_1 = require("./lab-content-importer");
class Ece4180Stack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // S3 Bucket for general video storage
        const videoBucket = new s3.Bucket(this, 'VideoBucket', {
            bucketName: 'ece4180-lab-videos',
            cors: [
                {
                    allowedMethods: [
                        s3.HttpMethods.GET,
                        s3.HttpMethods.POST,
                        s3.HttpMethods.PUT,
                    ],
                    allowedOrigins: ['*'],
                    allowedHeaders: ['*'],
                },
            ],
            lifecycleRules: [
                {
                    id: 'DeleteOldVideos',
                    expiration: cdk.Duration.days(365), // Keep videos for 1 year
                },
            ],
        });
        // S3 Bucket for checkoff videos (per lab part)
        const checkoffVideoBucket = new s3.Bucket(this, 'CheckoffVideoBucket', {
            bucketName: 'ece4180-checkoff-videos',
            cors: [
                {
                    allowedMethods: [
                        s3.HttpMethods.GET,
                        s3.HttpMethods.POST,
                        s3.HttpMethods.PUT,
                        s3.HttpMethods.HEAD,
                    ],
                    allowedOrigins: [
                        'http://localhost:3000',
                        'https://ece4180.vercel.app',
                        'https://ece-embedded-systems.vercel.app',
                        'https://embedded-website-2.vercel.app',
                        'https://embedded-website-2-git-main.vercel.app',
                        '*'
                    ],
                    allowedHeaders: ['*'],
                    exposedHeaders: [
                        'ETag',
                        'Content-Type',
                        'Content-Length',
                        'Content-Disposition',
                        'Content-Encoding'
                    ],
                    maxAge: 3600,
                },
            ],
            lifecycleRules: [
                {
                    id: 'DeleteOldCheckoffVideos',
                    expiration: cdk.Duration.days(365), // Keep videos for 1 year
                },
            ],
        });
        // DynamoDB Tables
        const submissionsTable = new dynamodb.Table(this, 'SubmissionsTable', {
            tableName: 'ece4180-submissions',
            partitionKey: { name: 'submissionId', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Table for tracking video submissions per lab part
        const partSubmissionsTable = new dynamodb.Table(this, 'PartSubmissionsTable', {
            tableName: 'ece4180-part-submissions',
            partitionKey: { name: 'submissionId', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Add GSI for querying by student
        partSubmissionsTable.addGlobalSecondaryIndex({
            indexName: 'StudentIndex',
            partitionKey: { name: 'studentId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'submittedAt', type: dynamodb.AttributeType.STRING },
        });
        // Add GSI for querying by lab and part
        partSubmissionsTable.addGlobalSecondaryIndex({
            indexName: 'LabPartIndex',
            partitionKey: { name: 'labId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'partId', type: dynamodb.AttributeType.STRING },
        });
        // Add GSI for querying by status (for the queue)
        partSubmissionsTable.addGlobalSecondaryIndex({
            indexName: 'StatusIndex',
            partitionKey: { name: 'status', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'submittedAt', type: dynamodb.AttributeType.STRING },
        });
        // Add GSI for querying by student
        submissionsTable.addGlobalSecondaryIndex({
            indexName: 'StudentIndex',
            partitionKey: { name: 'studentId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'submittedAt', type: dynamodb.AttributeType.STRING },
        });
        // Add GSI for querying by lab and status
        submissionsTable.addGlobalSecondaryIndex({
            indexName: 'LabStatusIndex',
            partitionKey: { name: 'labNumber', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'status', type: dynamodb.AttributeType.STRING },
        });
        const labStatusTable = new dynamodb.Table(this, 'LabStatusTable', {
            tableName: 'ece4180-lab-status-v2',
            partitionKey: { name: 'studentId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'labId', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Labs table for storing lab details
        const labsTable = new dynamodb.Table(this, 'LabsTable', {
            tableName: 'ece4180-labs-v1',
            partitionKey: { name: 'labId', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Students table for storing student information
        const studentsTable = new dynamodb.Table(this, 'StudentsTable', {
            tableName: 'ece4180-students',
            partitionKey: { name: 'name', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Add GSI for querying by section
        studentsTable.addGlobalSecondaryIndex({
            indexName: 'SectionIndex',
            partitionKey: { name: 'section', type: dynamodb.AttributeType.STRING },
        });
        // Lab Progress table for tracking detailed progress on lab parts
        const labProgressTable = new dynamodb.Table(this, 'LabProgressTable', {
            tableName: 'ece4180-lab-progress',
            partitionKey: { name: 'studentId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'progressId', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Lab Grades table for storing grades for each lab
        const labGradesTable = new dynamodb.Table(this, 'LabGradesTable', {
            tableName: 'ece4180-lab-grades',
            partitionKey: { name: 'studentId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'labId', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
        // Add GSI for querying labs by order
        labsTable.addGlobalSecondaryIndex({
            indexName: 'OrderIndex',
            partitionKey: { name: 'order', type: dynamodb.AttributeType.NUMBER },
        });
        // Cognito User Pool
        const userPool = new cognito.UserPool(this, 'UserPool', {
            userPoolName: 'ece4180-users',
            selfSignUpEnabled: true,
            signInAliases: {
                email: true,
            },
            autoVerify: {
                email: true,
            },
            standardAttributes: {
                email: {
                    required: true,
                    mutable: true,
                },
            },
            customAttributes: {
                role: new cognito.StringAttribute({ mutable: true }),
                studentId: new cognito.StringAttribute({ mutable: true }),
                fullName: new cognito.StringAttribute({ mutable: true }),
            },
            passwordPolicy: {
                minLength: 8,
                requireLowercase: true,
                requireUppercase: true,
                requireDigits: true,
            },
            accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
        });
        // Cognito User Pool Client
        const userPoolClient = new cognito.UserPoolClient(this, 'UserPoolClient', {
            userPool,
            authFlows: {
                adminUserPassword: true,
                custom: true,
                userSrp: true,
                userPassword: true, // Enable USER_PASSWORD_AUTH flow
            },
            supportedIdentityProviders: [
                cognito.UserPoolClientIdentityProvider.COGNITO,
            ],
        });
        // Lambda execution role
        const lambdaRole = new iam.Role(this, 'LambdaExecutionRole', {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
            ],
            inlinePolicies: {
                DynamoDBAccess: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                'dynamodb:GetItem',
                                'dynamodb:PutItem',
                                'dynamodb:UpdateItem',
                                'dynamodb:DeleteItem',
                                'dynamodb:Query',
                                'dynamodb:Scan',
                            ],
                            resources: [
                                submissionsTable.tableArn,
                                `${submissionsTable.tableArn}/index/*`,
                                partSubmissionsTable.tableArn,
                                `${partSubmissionsTable.tableArn}/index/*`,
                                labStatusTable.tableArn,
                                labsTable.tableArn,
                                `${labsTable.tableArn}/index/*`,
                                studentsTable.tableArn,
                                `${studentsTable.tableArn}/index/*`,
                                labProgressTable.tableArn,
                                labGradesTable.tableArn,
                            ],
                        }),
                    ],
                }),
                S3Access: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                's3:GetObject',
                                's3:PutObject',
                                's3:DeleteObject',
                                's3:GetSignedUrl',
                            ],
                            resources: [
                                `${videoBucket.bucketArn}/*`,
                                `${checkoffVideoBucket.bucketArn}/*`
                            ],
                        }),
                    ],
                }),
                CognitoAccess: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                'cognito-idp:AdminGetUser',
                                'cognito-idp:AdminUpdateUserAttributes',
                            ],
                            resources: [userPool.userPoolArn],
                        }),
                    ],
                }),
            },
        });
        // Lambda Functions
        const authFunction = new lambda.Function(this, 'AuthFunction', {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'auth.handler',
            code: lambda.Code.fromAsset('lambda'),
            role: lambdaRole,
            environment: {
                USER_POOL_ID: userPool.userPoolId,
                USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
            },
        });
        const labsFunction = new lambda.Function(this, 'LabsFunction', {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'labs.handler',
            code: lambda.Code.fromAsset('lambda'),
            role: lambdaRole,
            environment: {
                LAB_STATUS_TABLE: labStatusTable.tableName,
                LABS_TABLE: labsTable.tableName,
                SUBMISSIONS_TABLE: submissionsTable.tableName,
                PART_SUBMISSIONS_TABLE: partSubmissionsTable.tableName,
                VIDEO_BUCKET: videoBucket.bucketName,
                CHECKOFF_VIDEO_BUCKET: checkoffVideoBucket.bucketName,
                STUDENTS_TABLE: studentsTable.tableName,
                LAB_PROGRESS_TABLE: labProgressTable.tableName,
                LAB_GRADES_TABLE: labGradesTable.tableName,
                USER_POOL_ID: userPool.userPoolId,
            },
        });
        const submissionsFunction = new lambda.Function(this, 'SubmissionsFunction', {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'submissions.handler',
            code: lambda.Code.fromAsset('lambda'),
            role: lambdaRole,
            environment: {
                SUBMISSIONS_TABLE: submissionsTable.tableName,
                PART_SUBMISSIONS_TABLE: partSubmissionsTable.tableName,
                VIDEO_BUCKET: videoBucket.bucketName,
                CHECKOFF_VIDEO_BUCKET: checkoffVideoBucket.bucketName,
                STUDENTS_TABLE: studentsTable.tableName,
                LAB_PROGRESS_TABLE: labProgressTable.tableName,
                LAB_GRADES_TABLE: labGradesTable.tableName,
            },
        });
        // Part Submissions Lambda function
        const partSubmissionsFunction = new lambda.Function(this, 'PartSubmissionsFunction', {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'part-submissions.handler',
            code: lambda.Code.fromAsset('lambda'),
            role: lambdaRole,
            environment: {
                PART_SUBMISSIONS_TABLE: partSubmissionsTable.tableName,
                CHECKOFF_VIDEO_BUCKET: checkoffVideoBucket.bucketName,
                STUDENTS_TABLE: studentsTable.tableName,
                LAB_PROGRESS_TABLE: labProgressTable.tableName,
                LAB_GRADES_TABLE: labGradesTable.tableName,
            },
        });
        // Students Lambda function
        const studentsFunction = new lambda.Function(this, 'StudentsFunction', {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'students.handler',
            code: lambda.Code.fromAsset('lambda'),
            role: lambdaRole,
            environment: {
                STUDENTS_TABLE: studentsTable.tableName,
                LAB_STATUS_TABLE: labStatusTable.tableName,
                LAB_PROGRESS_TABLE: labProgressTable.tableName,
                LAB_GRADES_TABLE: labGradesTable.tableName,
                LABS_TABLE: labsTable.tableName,
                SUBMISSIONS_TABLE: submissionsTable.tableName,
                PART_SUBMISSIONS_TABLE: partSubmissionsTable.tableName,
                VIDEO_BUCKET: videoBucket.bucketName,
                CHECKOFF_VIDEO_BUCKET: checkoffVideoBucket.bucketName,
                USER_POOL_ID: userPool.userPoolId,
            },
        });
        // API Gateway
        const api = new apigateway.RestApi(this, 'Ece4180Api', {
            restApiName: 'ECE 4180 Course API',
            description: 'API for ECE 4180 course platform',
            defaultCorsPreflightOptions: {
                allowOrigins: apigateway.Cors.ALL_ORIGINS,
                allowMethods: apigateway.Cors.ALL_METHODS,
                allowHeaders: [
                    ...apigateway.Cors.DEFAULT_HEADERS,
                    'Authorization',
                    'Content-Type',
                    'X-Amz-Date',
                    'X-Api-Key',
                    'X-Access-Token'
                ],
                allowCredentials: true,
            },
        });
        // Add CORS headers to 4XX error responses
        new apigateway.GatewayResponse(this, 'Default4XX', {
            restApi: api,
            type: apigateway.ResponseType.DEFAULT_4XX,
            responseHeaders: {
                'Access-Control-Allow-Origin': "'*'",
                'Access-Control-Allow-Headers': "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Access-Token'",
                'Access-Control-Allow-Methods': "'GET,POST,OPTIONS,PUT'",
                'Access-Control-Allow-Credentials': "'true'"
            }
        });
        // Add CORS headers to 5XX error responses
        new apigateway.GatewayResponse(this, 'Default5XX', {
            restApi: api,
            type: apigateway.ResponseType.DEFAULT_5XX,
            responseHeaders: {
                'Access-Control-Allow-Origin': "'*'",
                'Access-Control-Allow-Headers': "'Content-Type,Authorization,X-Amz-Date,X-Api-Key'",
                'Access-Control-Allow-Methods': "'GET,POST,OPTIONS,PUT'",
                'Access-Control-Allow-Credentials': "'true'"
            }
        });
        // Cognito Authorizer
        const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'CognitoAuthorizer', {
            cognitoUserPools: [userPool],
        });
        // API Routes
        const authResource = api.root.addResource('auth');
        authResource.addMethod('POST', new apigateway.LambdaIntegration(authFunction));
        // Add authenticated auth routes
        const authActionResource = authResource.addResource('{action}');
        authActionResource.addMethod('POST', new apigateway.LambdaIntegration(authFunction), {
            authorizer,
        });
        const labsResource = api.root.addResource('labs');
        labsResource.addMethod('GET', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        const labResource = labsResource.addResource('{labId}');
        labResource.addMethod('GET', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        labResource.addMethod('POST', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        // Add PUT method for updating lab content
        labResource.addMethod('PUT', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        const unlockResource = labResource.addResource('unlock');
        unlockResource.addMethod('POST', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        const lockResource = labResource.addResource('lock');
        lockResource.addMethod('POST', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        const submitResource = labResource.addResource('submit');
        submitResource.addMethod('POST', new apigateway.LambdaIntegration(labsFunction), {
            authorizer,
        });
        // Students API endpoints
        const studentsResource = api.root.addResource('students');
        studentsResource.addMethod('GET', new apigateway.LambdaIntegration(studentsFunction), {
            authorizer,
        });
        const studentResource = studentsResource.addResource('{studentName}');
        studentResource.addMethod('GET', new apigateway.LambdaIntegration(studentsFunction), {
            authorizer,
        });
        studentResource.addMethod('PUT', new apigateway.LambdaIntegration(studentsFunction), {
            authorizer,
        });
        // Progress API endpoints
        const progressResource = api.root.addResource('progress');
        progressResource.addMethod('GET', new apigateway.LambdaIntegration(studentsFunction), {
            authorizer,
        });
        const studentProgressResource = progressResource.addResource('{studentName}');
        studentProgressResource.addMethod('GET', new apigateway.LambdaIntegration(studentsFunction), {
            authorizer,
        });
        studentProgressResource.addMethod('PUT', new apigateway.LambdaIntegration(studentsFunction), {
            authorizer,
        });
        const submissionsResource = api.root.addResource('submissions');
        submissionsResource.addMethod('GET', new apigateway.LambdaIntegration(submissionsFunction), {
            authorizer,
        });
        const submissionResource = submissionsResource.addResource('{submissionId}');
        submissionResource.addMethod('PUT', new apigateway.LambdaIntegration(submissionsFunction), {
            authorizer,
        });
        // Part submissions API endpoints
        const partSubmissionsResource = api.root.addResource('part-submissions');
        partSubmissionsResource.addMethod('GET', new apigateway.LambdaIntegration(partSubmissionsFunction), {
            authorizer,
        });
        // Endpoint for creating a new part submission
        partSubmissionsResource.addMethod('POST', new apigateway.LambdaIntegration(partSubmissionsFunction), {
            authorizer,
        });
        // Endpoint for getting a specific part submission
        const partSubmissionResource = partSubmissionsResource.addResource('{submissionId}');
        partSubmissionResource.addMethod('GET', new apigateway.LambdaIntegration(partSubmissionsFunction), {
            authorizer,
        });
        // Endpoint for updating a part submission (approve/reject)
        partSubmissionResource.addMethod('PUT', new apigateway.LambdaIntegration(partSubmissionsFunction), {
            authorizer,
        });
        // Endpoint for getting the next submission in the queue
        const queueResource = partSubmissionsResource.addResource('queue');
        queueResource.addMethod('GET', new apigateway.LambdaIntegration(partSubmissionsFunction), {
            authorizer,
        });
        // Endpoint for getting a presigned URL for uploading a video
        const presignedUrlResource = partSubmissionsResource.addResource('presigned-url');
        presignedUrlResource.addMethod('POST', new apigateway.LambdaIntegration(partSubmissionsFunction), {
            authorizer,
        });
        // Outputs
        new cdk.CfnOutput(this, 'UserPoolId', {
            value: userPool.userPoolId,
            description: 'Cognito User Pool ID',
        });
        new cdk.CfnOutput(this, 'UserPoolClientId', {
            value: userPoolClient.userPoolClientId,
            description: 'Cognito User Pool Client ID',
        });
        new cdk.CfnOutput(this, 'ApiGatewayUrl', {
            value: api.url,
            description: 'API Gateway URL',
        });
        new cdk.CfnOutput(this, 'VideoBucketName', {
            value: videoBucket.bucketName,
            description: 'S3 Video Bucket Name',
        });
        new cdk.CfnOutput(this, 'CheckoffVideoBucketName', {
            value: checkoffVideoBucket.bucketName,
            description: 'S3 Checkoff Video Bucket Name',
        });
        // Add the lab content importer to automatically load lab content from JSON files
        // This will preserve the "locked" status of existing labs
        new lab_content_importer_1.LabContentImporter(this, 'LabContentImporter', {
            labsTable: labsTable
        });
    }
}
exports.Ece4180Stack = Ece4180Stack;
//# sourceMappingURL=data:application/json;base64,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