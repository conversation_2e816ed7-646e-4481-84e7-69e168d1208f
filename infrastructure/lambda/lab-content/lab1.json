{"content": "Build circuits and write code to interface with various input and output devices using the ESP32-C6. This lab covers digital I/O, analog signals, motor control, and multi-component integration.", "structuredContent": {"resources": [{"description": "Official ESP32 servo library for controlling servo motors.", "id": "esp32-servo-library", "type": "library", "title": "ESP32Servo Library", "url": "https://github.com/madhephaestus/ESP32Servo"}], "sections": [{"id": "intro", "type": "introduction", "title": "Introduction", "content": [{"type": "text", "content": "This lab introduces fundamental input/output operations on the ESP32-C6. You will work with digital inputs, outputs, analog signals, and various actuators to build practical embedded systems. Each part demonstrates core concepts that will be used throughout the course."}], "order": 1}, {"id": "requirements", "type": "requirements", "title": "Required Parts", "content": [{"type": "text", "content": "**From Kit:**\n- LED\n- 2 Pushbuttons\n- DIP Switch\n- Servo\n- H-Bridge\n- Potentiometer\n- DC Motor\n- Speaker\n- <PERSON><PERSON>\n- AC Wall Adapter\n- MEMS Microphone\n- Breadboard and jumper wires\n\n**From Lab:**\n- Navigation Switch\n- Oscilloscope (for Part 7)"}], "order": 2}, {"id": "part1", "type": "instructions", "title": "Part 1: Pushbutton LED (10 points)", "content": [{"type": "text", "content": "Build a circuit and write code so that pressing a pushbutton turns on an external LED.\n\n- Releasing the button should turn the LED off\n- You must use the internal pull-down resistor for the button"}], "order": 4}, {"id": "part2", "type": "instructions", "title": "Part 2: Direct GPIO Control (15 points)", "content": [{"type": "text", "content": "Repeat Part 1, but do not use API calls.\n\n- Use the internal pull-down resistor for the button\n- You must use **GPIO7** as the digital output\n- You will find that it requires another step besides the example shown in the slides"}], "order": 5}, {"id": "part2-5", "type": "instructions", "title": "Part 2.5: Assembly Implementation (10 points, Extra Credit)", "content": [{"type": "text", "content": "Repeat Part 2 in RISC-V Assembly."}], "order": 6}, {"id": "part3", "type": "instructions", "title": "Part 3: Dual Pushbutton LED Control (10 points)", "content": [{"type": "text", "content": "Use two pushbuttons to cycle through onboard RGB LED colors:\n\n- One button cycles forward, the other cycles backward\n- The order must be R → G → B → Y → R (and repeat)\n- Ensure the LED transitions cleanly without skipping states"}], "order": 7}, {"id": "part4", "type": "instructions", "title": "Part 4: Navigation Switch Input (10 points)", "content": [{"type": "text", "content": "Read input from the navigation switch and print the direction(s) to the serial monitor.\n\n- Multiple directions (e.g., \"top-left\") should be detected and displayed\n- Detect and display a center press as well"}], "order": 8}, {"id": "part5", "type": "instructions", "title": "Part 5: LED Brightness with Microphone (10 points)", "content": [{"type": "text", "content": "Use the MEMS microphone to adjust the brightness of an external LED. Note, the LED itself is quite finicky so don't worry too much if it isn't super obvious.\n\n- The microphone is an analog input\n- Control the LED with PWM output\n- When there is active audio, the LED should become brighter\n- The microphone may need some soldering"}], "order": 9}, {"id": "part6", "type": "instructions", "title": "Part 6: DC Motor Speed Control (10 points)", "content": [{"type": "text", "content": "Use an H-Bridge and potentiometer to control a DC motor:\n\n- When the potentiometer is centered, the motor is off\n- Turning the potentiometer one way increases the speed in one direction\n- Turning the other way increases speed in the opposite direction\n- You may need to use the barrel jack with a wall connection to power the circuit"}], "order": 10}, {"id": "part7", "type": "instructions", "title": "Part 7: Square Wave Generation (10 points)", "content": [{"type": "text", "content": "Generate a 2 kHz square wave. Capture and measure the signal using an oscilloscope."}], "order": 11}, {"id": "part8", "type": "instructions", "title": "Part 8: <PERSON><PERSON> with DIP Switch (10 points)", "content": [{"type": "text", "content": "Control a servo motor using a DIP switch to select among four distinct angles. Use the ESP32Servo Library to help control it."}], "order": 12}, {"id": "part9", "type": "instructions", "title": "Part 9: Race Start Countdown (15 points)", "content": [{"type": "text", "content": "Create a race start countdown sequence (similar to Mario <PERSON>), using a button, speaker, motor, and the on-board RGB LED.\n\nWhen a button is pressed, the system runs a four-second sequence:\n\n- **Seconds 1-3**: LED flashes red, motor speed increases, and the speaker beeps\n- **Second 4 (\"GO\")**: LED flashes green, motor reaches maximum speed, and the speaker plays a higher-pitch beep\n- You may need to use the barrel jack with a wall connection to power the circuit\n\nThe sequence should reset and be repeatable on each button press."}], "order": 13}, {"id": "submission", "type": "submission", "title": "Submission Requirements", "content": [{"type": "text", "content": "For each part, demonstrate your working circuit to a member of the instructional staff. Be prepared to explain your code and circuit design."}], "order": 14}]}, "labId": "lab1", "updatedAt": "2025-09-02T17:27:59.972Z", "locked": true, "status": "locked", "createdAt": "2025-08-22T18:28:00.000Z", "order": 1, "description": "Build circuits and write code to interface with various input and output devices using the ESP32-C6. This lab covers digital I/O, analog signals, motor control, and multi-component integration.", "title": "Lab 1: Input/Output Fundamentals"}