# ECE 4180 Embedded Systems Course Platform - Implementation Plan

## Project Overview
Comprehensive course website with secure lab submission system using AWS CDK, API Gateway, Cognito, and React/TypeScript/Tailwind.

## Architecture Requirements
- **Infrastructure**: AWS CDK for IaC
- **Authentication**: AWS Cognito with Staff/Student roles
- **API**: API Gateway with Lambda functions
- **Frontend**: React/TypeScript/Tailwind
- **Storage**: S3 for videos, DynamoDB for submissions
- **Region**: us-east-1

## Key Features
1. **Course Materials Section**: Syllabus, resources, general course info
2. **6 Lab Sections**: Individual navigation items, locked until staff unlocks
3. **Lab Progression**: Lab 1 (Assembly) → Labs 2-6 (Video checkoffs required)
4. **Role-Based Access**: Staff can unlock labs, students submit videos
5. **Video Submission System**: Secure upload/review workflow
6. **ESP32-C6 DevKit Focus**: Course content tailored to this platform

## Implementation Phases

### Phase 1: Infrastructure Setup
- [ ] Initialize CDK project
- [ ] Set up Cognito User Pool with custom attributes
- [ ] Create API Gateway with Lambda functions
- [ ] Configure S3 bucket for video storage
- [ ] Set up DynamoDB tables (submissions, lab-status)

### Phase 2: Authentication & Authorization
- [ ] Implement Cognito authentication flow
- [ ] Create role-based access control (Staff/Student)
- [ ] Set up protected routes in React
- [ ] Add user management for staff

### Phase 3: Frontend Foundation
- [ ] Initialize React/TypeScript project
- [ ] Set up Tailwind CSS
- [ ] Create navigation with 7 sections (Course Materials + 6 Labs)
- [ ] Implement responsive layout
- [ ] Add authentication components

### Phase 4: Course Content Management
- [ ] Create course materials section
- [ ] Design lab instruction templates
- [ ] Implement lab locking/unlocking system
- [ ] Add placeholder content for 6 labs
- [ ] Create ESP32-C6 specific content

### Phase 5: Lab Submission System
- [ ] Implement secure video upload (Labs 2-6)
- [ ] Create submission review interface for staff
- [ ] Add email notifications
- [ ] Implement submission status tracking
- [ ] Create archive/history views

### Phase 6: Testing & Deployment
- [ ] Unit tests for Lambda functions
- [ ] Integration tests for API endpoints
- [ ] Frontend component testing
- [ ] End-to-end testing
- [ ] CDK deployment pipeline

## Technical Specifications

### Database Schema
```
LabSubmissions Table:
- submissionId (PK)
- studentId
- labNumber
- partNumber
- videoKey
- status
- submittedAt
- reviewedBy
- feedback

LabStatus Table:
- labNumber (PK)
- isUnlocked
- unlockedBy
- unlockedAt
- instructions
```

### API Endpoints
```
POST /auth/login
POST /auth/logout
GET /labs/status
POST /labs/{labId}/unlock (Staff only)
POST /labs/{labId}/submit
GET /submissions (Staff only)
PUT /submissions/{id}/review (Staff only)
```

### Lab Structure
1. **Lab 1**: Assembly Programming (ESP32-C6)
2. **Lab 2**: GPIO and Digital I/O
3. **Lab 3**: Analog Input/PWM
4. **Lab 4**: Serial Communication
5. **Lab 5**: Wireless Communication
6. **Lab 6**: Final Project

## Security Considerations
- Cognito JWT tokens for authentication
- API Gateway authorizers
- S3 presigned URLs for video access
- Role-based permissions
- Input validation and sanitization
