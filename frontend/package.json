{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.865.0", "@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.3.0"}}