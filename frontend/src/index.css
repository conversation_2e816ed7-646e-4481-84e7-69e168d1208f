@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for lab cards */
.card {
  @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  min-height: 200px; /* Ensure minimum height for consistency */
  display: flex;
  flex-direction: column;
}

.btn-primary {
  @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50;
}

/* Custom styles for lab content */
.markdown-content ul,
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.markdown-content ol,
.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.markdown-content li,
.prose li {
  margin-bottom: 0.5rem;
}

/* Styles for sections that typically contain lists */
[data-section-type="objectives"] .prose ul,
[data-section-type="requirements"] .prose ul {
  margin-left: 0.5rem;
}

[data-section-type="objectives"] .prose li,
[data-section-type="requirements"] .prose li {
  padding-left: 0.5rem;
  margin-bottom: 0.75rem;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-bold py-2 px-4 rounded;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .input {
    @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
}
