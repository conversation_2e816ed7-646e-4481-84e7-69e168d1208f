# Name Collection Feature Testing Guide

## Overview
This document outlines the testing procedure for the new name collection feature that prompts users to enter their full name when they first sign in.

## Feature Components

### Backend Changes
1. **Cognito User Pool**: Added `custom:fullName` attribute
2. **Auth Lambda**: Added `update-attributes` endpoint
3. **API Gateway**: Added authenticated route for `/auth/{action}`

### Frontend Changes
1. **User Type**: Added optional `fullName` field
2. **AuthContext**: Added `updateUserAttributes` function and `showNameCollectionModal` state
3. **NameCollectionModal**: New modal component for name collection
4. **HomePage**: Updated to display full name instead of email
5. **App.tsx**: Added modal integration

## Testing Procedure

### Prerequisites
1. Deploy the updated infrastructure: `cd infrastructure && npm run deploy`
2. Build and deploy the frontend: `cd frontend && npm run build`
3. Ensure you have access to a test @gatech.edu email account

### Test Cases

#### Test Case 1: New User Registration and Name Collection
1. **Setup**: Use a new @gatech.edu email that hasn't been registered
2. **Steps**:
   - Navigate to the sign-up page
   - Register with the new email
   - Confirm the account via email verification
   - Sign in with the new account
3. **Expected Result**: 
   - After successful sign-in, the name collection modal should appear
   - Modal should display: "Welcome! To personalize your experience, please enter your full name as it appears on Canvas."
   - Modal should not be dismissible (no X button or click-outside-to-close)

#### Test Case 2: Name Submission and Storage
1. **Setup**: Continue from Test Case 1 with modal open
2. **Steps**:
   - Enter a valid full name (e.g., "John Smith")
   - Click "Continue" button
3. **Expected Result**:
   - Modal should show loading state
   - Modal should disappear after successful submission
   - Home page should display "Welcome back, John Smith!" instead of email
   - No errors should occur

#### Test Case 3: Name Validation
1. **Setup**: Continue from Test Case 1 with modal open
2. **Steps**:
   - Try submitting with empty name
   - Try submitting with single character
   - Try submitting with valid name
3. **Expected Result**:
   - Empty name: Error message "Please enter your full name"
   - Single character: Error message "Please enter a valid full name"
   - Valid name: Successful submission

#### Test Case 4: Existing User with Name
1. **Setup**: Use an account that already has a full name set
2. **Steps**:
   - Sign in with the existing account
3. **Expected Result**:
   - No modal should appear
   - Home page should display "Welcome back, [Full Name]!"

#### Test Case 5: Existing User without Name
1. **Setup**: Use an existing account that doesn't have a full name
2. **Steps**:
   - Sign in with the existing account
3. **Expected Result**:
   - Name collection modal should appear
   - Follow Test Case 2 to complete name submission

#### Test Case 6: Network Error Handling
1. **Setup**: Continue from Test Case 1 with modal open
2. **Steps**:
   - Disconnect from internet or block API calls
   - Try to submit a name
3. **Expected Result**:
   - Error message should appear
   - Modal should remain open
   - User can retry after fixing connection

#### Test Case 7: Session Persistence
1. **Setup**: Complete Test Case 2 successfully
2. **Steps**:
   - Refresh the page
   - Navigate to different pages
   - Sign out and sign back in
3. **Expected Result**:
   - Name should persist across page refreshes
   - Home page should continue showing full name
   - No modal should appear on subsequent sign-ins

## Backend API Testing

### Test the Update Attributes Endpoint
```bash
# Get access token from browser localStorage after signing in
ACCESS_TOKEN="your_access_token_here"

# Test the endpoint
curl -X POST https://your-api-endpoint.com/auth/update-attributes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{"fullName": "Test User Name"}'
```

Expected Response:
```json
{
  "message": "User attributes updated successfully",
  "fullName": "Test User Name"
}
```

## Verification Steps

### Database Verification
1. Check Cognito User Pool in AWS Console
2. Find the test user
3. Verify `custom:fullName` attribute is set correctly

### Frontend State Verification
1. Open browser developer tools
2. Check localStorage for tokens
3. Inspect AuthContext state in React DevTools
4. Verify `user.fullName` is populated

## Rollback Plan
If issues are found:
1. The feature is non-breaking - existing users without names will see the modal
2. To disable: Set `showNameCollectionModal: false` in AuthContext
3. To remove: Revert the commits and redeploy

## Success Criteria
- [ ] New users see the name collection modal
- [ ] Names are successfully stored in Cognito
- [ ] Home page displays full names
- [ ] Existing users are not affected
- [ ] Error handling works correctly
- [ ] Modal cannot be dismissed without entering a name
- [ ] Names persist across sessions
